import { FirebaseApp, initializeApp } from '@react-native-firebase/app';
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';

// Firebase configuration
// Note: Replace these with your actual Firebase config values
const firebaseConfig = {
  apiKey: "AIzaSyCO4Xd8WWQzu5dXfTAT8LLwcqLkE2rkVz0",
  authDomain: "advocate-diary-c44b1.firebaseapp.com",
  projectId: "advocate-diary-c44b1",
  storageBucket: "advocate-diary-c44b1.firebasestorage.app",
  messagingSenderId: "833552250300",
  appId: "1:833552250300:web:8b1d2f65376b8ac6d1e7b9",
  measurementId: "G-QNECH00KNB"
};

// Initialize Firebase
let app: FirebaseApp;

try {
  app = initializeApp(firebaseConfig);
} catch (error) {
  console.log('Firebase initialization error:', error);
}

// Export Firebase services
export { auth, firestore };
export default app;

// Export types
export type { FirebaseAuthTypes };

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface Case {
  id: string;
  title: string;
  clientName: string;
  caseNumber: string;
  nextHearing?: Date;
  status: 'active' | 'closed' | 'pending';
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  cases: string[]; // Case IDs
  createdAt: Date;
  updatedAt: Date;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  caseId?: string;
  clientId?: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Hearing {
  id: string;
  caseId: string;
  title: string;
  date: Date;
  time: string;
  court: string;
  judge?: string;
  notes?: string;
  status: 'scheduled' | 'completed' | 'postponed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

export type RootStackParamList = {
  Login: undefined;
  Dashboard: undefined;
  CaseDetails: { caseId: string };
  ClientDetails: { clientId: string };
  AddCase: undefined;
  AddClient: undefined;
  AddNote: undefined;
  Calendar: undefined;
  Profile: undefined;
};

{"name": "AdvocateDiary", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-firebase/app": "^22.4.0", "@react-native-firebase/auth": "^22.4.0", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native/new-app-screen": "0.80.1", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-navigation/stack": "^7.4.2", "nativewind": "^4.1.23", "react": "19.1.0", "react-native": "0.80.1", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^4.0.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.0.0", "@react-native-community/cli-platform-android": "19.0.0", "@react-native-community/cli-platform-ios": "19.0.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@tailwindcss/cli": "^4.0.0", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}
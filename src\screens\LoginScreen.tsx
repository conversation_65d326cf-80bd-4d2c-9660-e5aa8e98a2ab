import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
} from 'react-native';
import Animated, { 
  FadeInDown, 
  FadeInUp, 
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { useAuth } from '../store/AuthContext';
import '../styles/global.css';

const LoginScreen: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const { signInWithGoogle, signInWithEmail, signUpWithEmail, isLoading } = useAuth();

  const buttonScale = useSharedValue(1);

  const animatedButtonStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }],
    };
  });

  const handleGoogleSignIn = async () => {
    try {
      buttonScale.value = withSpring(0.95, {}, () => {
        buttonScale.value = withSpring(1);
      });
      await signInWithGoogle();
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to sign in with Google');
    }
  };

  const handleEmailAuth = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      if (isSignUp) {
        await signUpWithEmail(email, password);
      } else {
        await signInWithEmail(email, password);
      }
    } catch (error: any) {
      Alert.alert('Error', error.message || `Failed to ${isSignUp ? 'sign up' : 'sign in'}`);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-gradient-to-br from-primary-50 to-primary-100"
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        className="flex-1"
      >
        <View className="flex-1 justify-center px-6 py-12">
          {/* Logo and Title */}
          <Animated.View 
            entering={BounceIn.delay(200).duration(1000)}
            className="items-center mb-12"
          >
            <View className="w-24 h-24 bg-primary-600 rounded-full items-center justify-center mb-6 shadow-lg">
              <Text className="text-white text-3xl font-bold">AD</Text>
            </View>
            <Text className="text-3xl font-bold text-gray-900 text-center">
              Advocate Diary
            </Text>
            <Text className="text-lg text-gray-600 text-center mt-2">
              Your Digital Legal Companion
            </Text>
          </Animated.View>

          {/* Login Form */}
          <Animated.View 
            entering={FadeInUp.delay(400).duration(800)}
            className="space-y-6"
          >
            {/* Email Input */}
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-2">Email</Text>
              <TextInput
                className="input-field"
                placeholder="Enter your email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            {/* Password Input */}
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-2">Password</Text>
              <TextInput
                className="input-field"
                placeholder="Enter your password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            {/* Email Auth Button */}
            <TouchableOpacity
              onPress={handleEmailAuth}
              disabled={isLoading}
              className="btn-primary"
            >
              <Text className="text-white text-center font-semibold text-lg">
                {isLoading ? 'Please wait...' : (isSignUp ? 'Sign Up' : 'Sign In')}
              </Text>
            </TouchableOpacity>

            {/* Toggle Sign Up/Sign In */}
            <TouchableOpacity
              onPress={() => setIsSignUp(!isSignUp)}
              className="py-2"
            >
              <Text className="text-primary-600 text-center font-medium">
                {isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up"}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Divider */}
          <Animated.View 
            entering={FadeInDown.delay(600).duration(800)}
            className="flex-row items-center my-8"
          >
            <View className="flex-1 h-px bg-gray-300" />
            <Text className="mx-4 text-gray-500 font-medium">OR</Text>
            <View className="flex-1 h-px bg-gray-300" />
          </Animated.View>

          {/* Google Sign In Button */}
          <Animated.View 
            entering={FadeInDown.delay(800).duration(800)}
            style={animatedButtonStyle}
          >
            <TouchableOpacity
              onPress={handleGoogleSignIn}
              disabled={isLoading}
              className="bg-white border border-gray-300 rounded-lg px-6 py-4 flex-row items-center justify-center shadow-sm active:bg-gray-50"
            >
              <View className="w-5 h-5 mr-3 bg-red-500 rounded-full" />
              <Text className="text-gray-700 font-semibold text-lg">
                Continue with Google
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Footer */}
          <Animated.View 
            entering={FadeInDown.delay(1000).duration(800)}
            className="mt-12"
          >
            <Text className="text-center text-gray-500 text-sm">
              By continuing, you agree to our Terms of Service and Privacy Policy
            </Text>
          </Animated.View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default LoginScreen;

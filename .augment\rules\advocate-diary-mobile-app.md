---
type: "always_apply"
description: "Example description"
---
# Advocate Digital Diary App

A React Native CLI mobile application built for Advocates and Lawyers to manage their daily routine, case schedules, client records, and notes — all in one digital diary.

## Project Purpose

This app simplifies the day-to-day life of advocates by providing a digital solution for:
- Case and hearing tracking
- Client and case records
- Notes, reminders, and documents
- Calendar-based legal diary

## Tech Stack

- React Native CLI
- Tailwind CSS v4 (via NativeWind)
- Firebase (Authentication, Firestore, Storage, Messaging)
- React Navigation v6
- React Native Reanimated (for animations)
- AsyncStorage or MMKV for offline storage

## Project Structure

/src
  /assets
  /components
  /screens
  /navigation
  /services
  /store
  /hooks
  /types
  /config
  /utils
  /styles

## UI Styling with Tailwind CSS

- Use NativeWind to apply Tailwind CSS v4 classes.
- Prefer class-based styling (no inline styles).
- Dark mode supported.

## Animations

- Use react-native-reanimated for screen transitions and UI effects.
- Keep animations minimal and user-friendly.

## Authentication

- Firebase Email/Password authentication.
- Fingerprint/Face ID login (optional).

## Notifications

- Local notifications using expo-notifications or FCM.
- For upcoming hearing reminders and task alerts.

## MVP Features

1. Case Calendar - add and view daily/weekly/monthly hearing schedules.
2. Client and Case Management - manage client details and link cases.
3. Notes and Documents - text notes and file uploads.
4. Dashboard - overview of today's cases and upcoming schedules.

## Development Guidelines

- Functional components with hooks.
- Modular and reusable code.
- Mobile responsive layouts.
- Firebase functions should be abstracted inside /services.
- Tailwind should be used for all styling.

## Security

- Never hardcode Firebase credentials.
- Secure Firestore access by user ID.
- Use MMKV or encrypted storage for local sensitive data.

## Setup Instructions

1. Clone the repository.
2. Run: npm install
3. Set up Firebase config under /config/firebase.ts
4. Start the app: npx react-native run-android or run-ios

## AI Code Agent Instructions

- Follow the folder structure and naming conventions.
- Use Tailwind v4 for all styling.
- Add subtle animations where relevant.
- Implement secure and abstracted Firebase usage.
- Keep code modular and responsive.

@import "tailwindcss";

/* Custom base styles */
@layer base {
  * {
    box-sizing: border-box;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold shadow-lg active:bg-primary-700 disabled:bg-gray-400 disabled:opacity-50;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 text-secondary-800 px-6 py-3 rounded-lg font-semibold active:bg-secondary-300 disabled:bg-gray-200 disabled:opacity-50;
  }
  
  .btn-outline {
    @apply border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold active:bg-primary-50 disabled:border-gray-400 disabled:text-gray-400;
  }
  
  .input-field {
    @apply border border-gray-300 rounded-lg px-4 py-3 text-base bg-white focus:border-primary-500 focus:ring-2 focus:ring-primary-200;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-md p-6 border border-gray-100;
  }
  
  .card-header {
    @apply text-xl font-bold text-gray-900 mb-4;
  }
  
  .text-heading {
    @apply text-2xl font-bold text-gray-900;
  }
  
  .text-subheading {
    @apply text-lg font-semibold text-gray-700;
  }
  
  .text-body {
    @apply text-base text-gray-600;
  }
  
  .text-caption {
    @apply text-sm text-gray-500;
  }
}

/* Custom utility styles */
@layer utilities {
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

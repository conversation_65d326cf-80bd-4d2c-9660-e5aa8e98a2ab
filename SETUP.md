# Advocate Diary - Setup Instructions

## Firebase Configuration

To complete the setup, you need to configure Firebase:

### 1. Create a Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or use an existing one
3. Enable Authentication and Firestore Database

### 2. Configure Authentication

1. In Firebase Console, go to Authentication > Sign-in method
2. Enable Email/Password authentication
3. Enable Google authentication
4. Add your app's SHA-1 fingerprint for Android

### 3. Get Configuration Values

Update the following files with your Firebase configuration:

#### `src/config/firebase.ts`
Replace the placeholder values with your actual Firebase config:

```typescript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-messaging-sender-id",
  appId: "your-app-id",
};
```

#### `src/services/authService.ts`
Update the Google Sign-In configuration:

```typescript
GoogleSignin.configure({
  webClientId: 'your-web-client-id.googleusercontent.com',
  offlineAccess: true,
});
```

### 4. Android Setup

1. Download `google-services.json` from Firebase Console
2. Place it in `android/app/` directory
3. Add SHA-1 fingerprint to Firebase project

### 5. iOS Setup

1. Download `GoogleService-Info.plist` from Firebase Console
2. Add it to your iOS project in Xcode
3. Configure URL schemes in Info.plist

## Running the App

1. Install dependencies: `npm install`
2. For Android: `npx react-native run-android`
3. For iOS: `npx react-native run-ios`

## Features Implemented

- ✅ Google Authentication with Firebase
- ✅ Email/Password Authentication
- ✅ NativeWind with Tailwind CSS v4
- ✅ React Native Reanimated animations
- ✅ React Navigation with authentication flow
- ✅ TypeScript support
- ✅ Modern UI with smooth animations
- ✅ Dashboard with overview cards
- ✅ Responsive design

## Next Steps

After completing the Firebase setup, you can:

1. Add more screens (Case Management, Client Management, etc.)
2. Implement Firestore database operations
3. Add push notifications
4. Implement offline storage with MMKV
5. Add more authentication methods (biometric, etc.)

## Troubleshooting

- Make sure all Firebase configuration values are correct
- Ensure Google Play Services are available on Android device/emulator
- Check that SHA-1 fingerprint is added to Firebase project
- Verify that authentication methods are enabled in Firebase Console

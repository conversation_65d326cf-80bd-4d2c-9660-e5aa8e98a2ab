import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import Animated, { 
  FadeInDown, 
  FadeInRight,
  SlideInLeft,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { useAuth } from '../store/AuthContext';
import '../styles/global.css';

const DashboardScreen: React.FC = () => {
  const { user, signOut } = useAuth();

  const cardScale = useSharedValue(1);

  const animatedCardStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: cardScale.value }],
    };
  });

  const handleCardPress = () => {
    cardScale.value = withSpring(0.95, {}, () => {
      cardScale.value = withSpring(1);
    });
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const quickActions = [
    { id: 1, title: 'Add Case', icon: '📁', color: 'bg-blue-500' },
    { id: 2, title: 'Add Client', icon: '👤', color: 'bg-green-500' },
    { id: 3, title: 'Schedule Hearing', icon: '📅', color: 'bg-purple-500' },
    { id: 4, title: 'Add Note', icon: '📝', color: 'bg-orange-500' },
  ];

  const todayStats = [
    { label: 'Today\'s Hearings', value: '3', color: 'text-blue-600' },
    { label: 'Active Cases', value: '12', color: 'text-green-600' },
    { label: 'Pending Tasks', value: '5', color: 'text-orange-600' },
    { label: 'Total Clients', value: '28', color: 'text-purple-600' },
  ];

  const upcomingHearings = [
    {
      id: 1,
      case: 'Smith vs. Johnson',
      time: '10:00 AM',
      court: 'District Court A',
      status: 'confirmed',
    },
    {
      id: 2,
      case: 'Property Dispute - ABC Corp',
      time: '2:30 PM',
      court: 'High Court',
      status: 'pending',
    },
    {
      id: 3,
      case: 'Criminal Case - State vs. Doe',
      time: '4:00 PM',
      court: 'Sessions Court',
      status: 'confirmed',
    },
  ];

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Animated.View 
          entering={SlideInLeft.duration(800)}
          className="bg-primary-600 px-6 py-8 rounded-b-3xl"
        >
          <View className="flex-row justify-between items-center">
            <View>
              <Text className="text-white text-lg font-medium">Welcome back,</Text>
              <Text className="text-white text-2xl font-bold">
                {user?.displayName || user?.email?.split('@')[0] || 'Advocate'}
              </Text>
            </View>
            <TouchableOpacity
              onPress={handleSignOut}
              className="bg-white/20 px-4 py-2 rounded-lg"
            >
              <Text className="text-white font-medium">Sign Out</Text>
            </TouchableOpacity>
          </View>
          
          <View className="mt-6">
            <Text className="text-white/90 text-base">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </Text>
          </View>
        </Animated.View>

        {/* Stats Cards */}
        <Animated.View 
          entering={FadeInDown.delay(200).duration(800)}
          className="px-6 -mt-6"
        >
          <View className="bg-white rounded-2xl shadow-lg p-6">
            <Text className="text-lg font-bold text-gray-900 mb-4">Today's Overview</Text>
            <View className="flex-row flex-wrap justify-between">
              {todayStats.map((stat, index) => (
                <Animated.View
                  key={stat.label}
                  entering={FadeInRight.delay(300 + index * 100).duration(600)}
                  className="w-[48%] mb-4"
                >
                  <Text className={`text-2xl font-bold ${stat.color}`}>{stat.value}</Text>
                  <Text className="text-gray-600 text-sm">{stat.label}</Text>
                </Animated.View>
              ))}
            </View>
          </View>
        </Animated.View>

        {/* Quick Actions */}
        <Animated.View 
          entering={FadeInDown.delay(400).duration(800)}
          className="px-6 mt-6"
        >
          <Text className="text-lg font-bold text-gray-900 mb-4">Quick Actions</Text>
          <View className="flex-row flex-wrap justify-between">
            {quickActions.map((action, index) => (
              <Animated.View
                key={action.id}
                entering={FadeInDown.delay(500 + index * 100).duration(600)}
                style={animatedCardStyle}
                className="w-[48%] mb-4"
              >
                <TouchableOpacity
                  onPress={handleCardPress}
                  className="bg-white rounded-xl p-4 shadow-md items-center"
                >
                  <View className={`w-12 h-12 ${action.color} rounded-full items-center justify-center mb-3`}>
                    <Text className="text-2xl">{action.icon}</Text>
                  </View>
                  <Text className="text-gray-900 font-medium text-center">{action.title}</Text>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Upcoming Hearings */}
        <Animated.View 
          entering={FadeInDown.delay(600).duration(800)}
          className="px-6 mt-6 mb-8"
        >
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-lg font-bold text-gray-900">Today's Hearings</Text>
            <TouchableOpacity>
              <Text className="text-primary-600 font-medium">View All</Text>
            </TouchableOpacity>
          </View>
          
          <View className="space-y-3">
            {upcomingHearings.map((hearing, index) => (
              <Animated.View
                key={hearing.id}
                entering={FadeInRight.delay(700 + index * 100).duration(600)}
                className="bg-white rounded-xl p-4 shadow-md"
              >
                <View className="flex-row justify-between items-start">
                  <View className="flex-1">
                    <Text className="text-gray-900 font-semibold text-base mb-1">
                      {hearing.case}
                    </Text>
                    <Text className="text-gray-600 text-sm mb-2">{hearing.court}</Text>
                    <View className="flex-row items-center">
                      <Text className="text-primary-600 font-medium">{hearing.time}</Text>
                      <View className={`ml-3 px-2 py-1 rounded-full ${
                        hearing.status === 'confirmed' ? 'bg-green-100' : 'bg-orange-100'
                      }`}>
                        <Text className={`text-xs font-medium ${
                          hearing.status === 'confirmed' ? 'text-green-800' : 'text-orange-800'
                        }`}>
                          {hearing.status}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <TouchableOpacity className="ml-4">
                    <Text className="text-2xl">📋</Text>
                  </TouchableOpacity>
                </View>
              </Animated.View>
            ))}
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default DashboardScreen;

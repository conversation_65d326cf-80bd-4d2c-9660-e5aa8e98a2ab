import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { auth, FirebaseAuthTypes } from '../config/firebase';
import { User } from '../types';

class AuthService {
  constructor() {
    this.configureGoogleSignIn();
  }

  private configureGoogleSignIn() {
    GoogleSignin.configure({
      webClientId: '833552250300-v1ehf92ell36su2018jin1nrrmobnll6.apps.googleusercontent.com',
      offlineAccess: true,
    });
  }

  async signInWithGoogle(): Promise<User | null> {
    try {
      // Check if your device supports Google Play
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      
      // Get the users ID token
      const { idToken } = await GoogleSignin.signIn();
      
      // Create a Google credential with the token
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      
      // Sign-in the user with the credential
      const userCredential = await auth().signInWithCredential(googleCredential);
      
      return this.mapFirebaseUserToUser(userCredential.user);
    } catch (error) {
      console.error('Google Sign-In Error:', error);
      throw error;
    }
  }

  async signInWithEmailAndPassword(email: string, password: string): Promise<User | null> {
    try {
      const userCredential = await auth().signInWithEmailAndPassword(email, password);
      return this.mapFirebaseUserToUser(userCredential.user);
    } catch (error) {
      console.error('Email Sign-In Error:', error);
      throw error;
    }
  }

  async createUserWithEmailAndPassword(email: string, password: string): Promise<User | null> {
    try {
      const userCredential = await auth().createUserWithEmailAndPassword(email, password);
      return this.mapFirebaseUserToUser(userCredential.user);
    } catch (error) {
      console.error('Email Sign-Up Error:', error);
      throw error;
    }
  }

  async signOut(): Promise<void> {
    try {
      // Sign out from Google
      const isSignedIn = await GoogleSignin.isSignedIn();
      if (isSignedIn) {
        await GoogleSignin.signOut();
      }
      
      // Sign out from Firebase
      await auth().signOut();
    } catch (error) {
      console.error('Sign-Out Error:', error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<User | null> {
    const firebaseUser = auth().currentUser;
    return firebaseUser ? this.mapFirebaseUserToUser(firebaseUser) : null;
  }

  onAuthStateChanged(callback: (user: User | null) => void): () => void {
    return auth().onAuthStateChanged((firebaseUser) => {
      const user = firebaseUser ? this.mapFirebaseUserToUser(firebaseUser) : null;
      callback(user);
    });
  }

  async sendPasswordResetEmail(email: string): Promise<void> {
    try {
      await auth().sendPasswordResetEmail(email);
    } catch (error) {
      console.error('Password Reset Error:', error);
      throw error;
    }
  }

  private mapFirebaseUserToUser(firebaseUser: FirebaseAuthTypes.User): User {
    return {
      uid: firebaseUser.uid,
      email: firebaseUser.email || '',
      displayName: firebaseUser.displayName || undefined,
      photoURL: firebaseUser.photoURL || undefined,
    };
  }
}

export default new AuthService();
